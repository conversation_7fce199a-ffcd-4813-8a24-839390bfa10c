import 'package:freezed_annotation/freezed_annotation.dart';

part 'income.freezed.dart';
part 'income.g.dart';

@freezed
class Income with _$Income {
  const factory Income({
    required int id,
    required String uuid,
    required DateTime date,
    required int initialMileage,
    required int finalMileage,
    required double initialGopay,
    required double initialBca,
    required double initialCash,
    required double initialOvo,
    required double initialBri,
    required double initialRekpon,
    required double finalGopay,
    required double finalBca,
    required double finalCash,
    required double finalOvo,
    required double finalBri,
    required double finalRekpon,
    required double initialCapital,
    required double finalResult,
    required int mileage,
    required double netIncome,
    required DateTime createdAt,
    required DateTime updatedAt,
    DateTime? deletedAt,
  }) = _Income;

  factory Income.fromJson(Map<String, dynamic> json) => _$IncomeFromJson(json);

  // Factory constructor to create Income from CSV row
  factory Income.fromCsvRow(List<dynamic> row) {
    return Income(
      id: int.parse(row[0].toString()),
      uuid: row[1].toString(),
      date: DateTime.parse(row[2].toString()),
      initialMileage: int.parse(row[3].toString()),
      finalMileage: int.parse(row[4].toString()),
      initialGopay: double.parse(row[5].toString()),
      initialBca: double.parse(row[6].toString()),
      initialCash: double.parse(row[7].toString()),
      initialOvo: double.parse(row[8].toString()),
      initialBri: double.parse(row[9].toString()),
      initialRekpon: double.parse(row[10].toString()),
      finalGopay: double.parse(row[11].toString()),
      finalBca: double.parse(row[12].toString()),
      finalCash: double.parse(row[13].toString()),
      finalOvo: double.parse(row[14].toString()),
      finalBri: double.parse(row[15].toString()),
      finalRekpon: double.parse(row[16].toString()),
      initialCapital: double.parse(row[17].toString()),
      finalResult: double.parse(row[18].toString()),
      mileage: int.parse(row[19].toString()),
      netIncome: double.parse(row[20].toString()),
      createdAt: DateTime.parse(row[21].toString()),
      updatedAt: DateTime.parse(row[22].toString()),
      deletedAt: row[23].toString().isEmpty ? null : DateTime.parse(row[23].toString()),
    );
  }
}

// Extension for additional computed properties
extension IncomeExtension on Income {
  // Calculate total initial amount across all payment methods
  double get totalInitialAmount =>
      initialGopay + initialBca + initialCash + initialOvo + initialBri + initialRekpon;

  // Calculate total final amount across all payment methods
  double get totalFinalAmount =>
      finalGopay + finalBca + finalCash + finalOvo + finalBri + finalRekpon;

  // Calculate the difference in each payment method
  double get gopayDifference => finalGopay - initialGopay;
  double get bcaDifference => finalBca - initialBca;
  double get cashDifference => finalCash - initialCash;
  double get ovoDifference => finalOvo - initialOvo;
  double get briDifference => finalBri - initialBri;
  double get rekponDifference => finalRekpon - initialRekpon;

  // Check if this income entry is profitable
  bool get isProfitable => netIncome > 0;

  // Get formatted date string
  String get formattedDate => '${date.day}/${date.month}/${date.year}';

  // Get formatted time string
  String get formattedTime => '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
}
