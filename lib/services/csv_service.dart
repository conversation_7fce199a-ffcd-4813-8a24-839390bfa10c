import 'dart:io';
import 'package:csv/csv.dart';
import 'package:flutter/services.dart';
import '../models/income.dart';

class CsvService {
  static const String _csvPath = 'New Folder/income.csv';

  /// Reads and parses the income CSV file
  /// Returns a list of Income objects
  /// Throws [CsvParsingException] if parsing fails
  static Future<List<Income>> loadIncomeData() async {
    try {
      // Read the CSV file content
      final String csvContent = await _readCsvFile();
      
      // Parse CSV content
      final List<List<dynamic>> csvData = const CsvToListConverter().convert(csvContent);
      
      // Validate CSV structure
      if (csvData.isEmpty) {
        throw CsvParsingException('CSV file is empty');
      }
      
      // Skip header row and convert to Income objects
      final List<Income> incomes = [];
      for (int i = 1; i < csvData.length; i++) {
        try {
          final income = Income.fromCsvRow(csvData[i]);
          incomes.add(income);
        } catch (e) {
          throw CsvParsingException('Error parsing row ${i + 1}: $e');
        }
      }
      
      return incomes;
    } catch (e) {
      if (e is CsvParsingException) {
        rethrow;
      }
      throw CsvParsingException('Failed to load income data: $e');
    }
  }

  /// Reads the CSV file content from assets or file system
  static Future<String> _readCsvFile() async {
    try {
      // First try to read from assets (for bundled files)
      try {
        return await rootBundle.loadString(_csvPath);
      } catch (e) {
        // If not in assets, try to read from file system
        final file = File(_csvPath);
        if (await file.exists()) {
          return await file.readAsString();
        } else {
          throw FileSystemException('CSV file not found at $_csvPath');
        }
      }
    } catch (e) {
      throw CsvParsingException('Failed to read CSV file: $e');
    }
  }

  /// Validates a CSV row has the expected number of columns
  static bool _isValidRow(List<dynamic> row) {
    // Expected columns based on CSV header
    const int expectedColumns = 24;
    return row.length == expectedColumns;
  }

  /// Filters income data by date range
  static List<Income> filterByDateRange(
    List<Income> incomes,
    DateTime? startDate,
    DateTime? endDate,
  ) {
    return incomes.where((income) {
      if (startDate != null && income.date.isBefore(startDate)) {
        return false;
      }
      if (endDate != null && income.date.isAfter(endDate)) {
        return false;
      }
      return true;
    }).toList();
  }

  /// Sorts income data by date (newest first by default)
  static List<Income> sortByDate(List<Income> incomes, {bool ascending = false}) {
    final sorted = List<Income>.from(incomes);
    sorted.sort((a, b) => ascending ? a.date.compareTo(b.date) : b.date.compareTo(a.date));
    return sorted;
  }

  /// Calculates summary statistics for a list of incomes
  static IncomeSummary calculateSummary(List<Income> incomes) {
    if (incomes.isEmpty) {
      return IncomeSummary.empty();
    }

    double totalIncome = 0;
    double totalExpenses = 0;
    int totalMileage = 0;
    int profitableTrips = 0;

    for (final income in incomes) {
      totalIncome += income.finalResult;
      totalExpenses += income.initialCapital;
      totalMileage += income.mileage;
      if (income.isProfitable) {
        profitableTrips++;
      }
    }

    return IncomeSummary(
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      netIncome: totalIncome - totalExpenses,
      totalMileage: totalMileage,
      totalTrips: incomes.length,
      profitableTrips: profitableTrips,
      averageIncomePerTrip: totalIncome / incomes.length,
      averageMileagePerTrip: totalMileage / incomes.length,
    );
  }
}

/// Exception thrown when CSV parsing fails
class CsvParsingException implements Exception {
  final String message;
  
  const CsvParsingException(this.message);
  
  @override
  String toString() => 'CsvParsingException: $message';
}

/// Summary statistics for income data
class IncomeSummary {
  final double totalIncome;
  final double totalExpenses;
  final double netIncome;
  final int totalMileage;
  final int totalTrips;
  final int profitableTrips;
  final double averageIncomePerTrip;
  final double averageMileagePerTrip;

  const IncomeSummary({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netIncome,
    required this.totalMileage,
    required this.totalTrips,
    required this.profitableTrips,
    required this.averageIncomePerTrip,
    required this.averageMileagePerTrip,
  });

  factory IncomeSummary.empty() => const IncomeSummary(
        totalIncome: 0,
        totalExpenses: 0,
        netIncome: 0,
        totalMileage: 0,
        totalTrips: 0,
        profitableTrips: 0,
        averageIncomePerTrip: 0,
        averageMileagePerTrip: 0,
      );

  double get profitableTripsPercentage =>
      totalTrips > 0 ? (profitableTrips / totalTrips) * 100 : 0;
}
