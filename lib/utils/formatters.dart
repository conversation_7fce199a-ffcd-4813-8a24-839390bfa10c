import 'package:intl/intl.dart';

class Formatters {
  // Indonesian Rupiah currency formatter
  static final NumberFormat _currencyFormatter = NumberFormat.currency(
    locale: 'id_ID',
    symbol: 'Rp ',
    decimalDigits: 0,
  );

  // Compact currency formatter for large numbers
  static final NumberFormat _compactCurrencyFormatter = NumberFormat.compactCurrency(
    locale: 'id_ID',
    symbol: 'Rp ',
    decimalDigits: 0,
  );

  // Date formatters
  static final DateFormat _dateFormatter = DateFormat('dd/MM/yyyy', 'id_ID');
  static final DateFormat _timeFormatter = DateFormat('HH:mm', 'id_ID');
  static final DateFormat _dateTimeFormatter = DateFormat('dd/MM/yyyy HH:mm', 'id_ID');
  static final DateFormat _monthYearFormatter = DateFormat('MMM yyyy', 'id_ID');
  static final DateFormat _dayMonthFormatter = DateFormat('dd MMM', 'id_ID');

  // Number formatters
  static final NumberFormat _numberFormatter = NumberFormat('#,##0', 'id_ID');
  static final NumberFormat _decimalFormatter = NumberFormat('#,##0.0', 'id_ID');

  /// Formats a number as Indonesian Rupiah currency
  /// Example: 50000 -> "Rp 50.000"
  static String formatCurrency(double amount) {
    return _currencyFormatter.format(amount);
  }

  /// Formats a number as compact Indonesian Rupiah currency
  /// Example: 1500000 -> "Rp 1,5 jt"
  static String formatCompactCurrency(double amount) {
    return _compactCurrencyFormatter.format(amount);
  }

  /// Formats currency with sign indicator for positive/negative values
  /// Example: 50000 -> "+Rp 50.000", -25000 -> "-Rp 25.000"
  static String formatCurrencyWithSign(double amount) {
    final formatted = _currencyFormatter.format(amount.abs());
    if (amount > 0) {
      return '+$formatted';
    } else if (amount < 0) {
      return '-$formatted';
    } else {
      return formatted;
    }
  }

  /// Formats a DateTime as date only
  /// Example: 2025-05-24 14:30:00 -> "24/05/2025"
  static String formatDate(DateTime date) {
    return _dateFormatter.format(date);
  }

  /// Formats a DateTime as time only
  /// Example: 2025-05-24 14:30:00 -> "14:30"
  static String formatTime(DateTime date) {
    return _timeFormatter.format(date);
  }

  /// Formats a DateTime as date and time
  /// Example: 2025-05-24 14:30:00 -> "24/05/2025 14:30"
  static String formatDateTime(DateTime date) {
    return _dateTimeFormatter.format(date);
  }

  /// Formats a DateTime as month and year
  /// Example: 2025-05-24 -> "Mei 2025"
  static String formatMonthYear(DateTime date) {
    return _monthYearFormatter.format(date);
  }

  /// Formats a DateTime as day and month
  /// Example: 2025-05-24 -> "24 Mei"
  static String formatDayMonth(DateTime date) {
    return _dayMonthFormatter.format(date);
  }

  /// Formats a number with thousand separators
  /// Example: 1234567 -> "1.234.567"
  static String formatNumber(int number) {
    return _numberFormatter.format(number);
  }

  /// Formats a decimal number with one decimal place
  /// Example: 123.456 -> "123,5"
  static String formatDecimal(double number) {
    return _decimalFormatter.format(number);
  }

  /// Formats percentage with one decimal place
  /// Example: 0.1234 -> "12,3%"
  static String formatPercentage(double percentage) {
    return '${_decimalFormatter.format(percentage * 100)}%';
  }

  /// Formats mileage with unit
  /// Example: 1500 -> "1.500 km"
  static String formatMileage(int mileage) {
    return '${formatNumber(mileage)} km';
  }

  /// Formats duration in a human-readable format
  /// Example: Duration(hours: 2, minutes: 30) -> "2j 30m"
  static String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}j ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// Formats a date range
  /// Example: (2025-05-01, 2025-05-31) -> "01/05/2025 - 31/05/2025"
  static String formatDateRange(DateTime startDate, DateTime endDate) {
    return '${formatDate(startDate)} - ${formatDate(endDate)}';
  }

  /// Formats a relative date (e.g., "2 days ago", "Today", "Tomorrow")
  static String formatRelativeDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);
    
    final difference = targetDate.difference(today).inDays;
    
    switch (difference) {
      case 0:
        return 'Hari ini';
      case 1:
        return 'Besok';
      case -1:
        return 'Kemarin';
      default:
        if (difference > 1) {
          return '$difference hari lagi';
        } else {
          return '${difference.abs()} hari yang lalu';
        }
    }
  }

  /// Formats income status with appropriate color coding info
  static String formatIncomeStatus(double netIncome) {
    if (netIncome > 0) {
      return 'Untung';
    } else if (netIncome < 0) {
      return 'Rugi';
    } else {
      return 'Impas';
    }
  }

  /// Formats trip efficiency (income per km)
  static String formatEfficiency(double income, int mileage) {
    if (mileage == 0) return 'N/A';
    final efficiency = income / mileage;
    return '${formatCurrency(efficiency)}/km';
  }
}
