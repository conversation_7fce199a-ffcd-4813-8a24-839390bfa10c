name: drivly
description: "A new Flutter project."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.8.1

dependencies:
  csv: ^6.0.0
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.6.1
  freezed: ^3.1.0
  freezed_annotation: ^3.1.0
  intl: ^0.20.2
  json_annotation: ^4.9.0
  riverpod_annotation: ^2.6.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4
  build_runner: ^2.5.4
  riverpod_generator: ^2.6.5
  json_serializable: ^6.9.5
  riverpod_lint: ^2.6.5
  custom_lint: ^0.7.5

flutter:
  uses-material-design: true
  assets:
    - assets/icon/
    - New Folder/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon/drivly_icon.png"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/icon/drivly_icon.png"
